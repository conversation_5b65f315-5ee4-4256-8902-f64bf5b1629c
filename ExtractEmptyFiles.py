# Разделяет страницы PDF на два файла: с данными и пустые, сохраняя их в отдельные файлы.

import fitz
import numpy as np
from datetime import datetime
import os
import asyncio


async def is_blank_async(page, threshold=0.99, white_level=250):
    """
    Проверяет, является ли страница пустой, анализируя содержимое пикселей.

    :param page: Объект страницы PyMuPDF.
    :param threshold: Доля пикселей, которые должны быть "белыми", чтобы страница считалась пустой (по умолчанию: 0.99).
    :param white_level: Значение пикселя, выше которого пиксель считается "белым" (по умолчанию: 250 из 255).
    :return: True, если страница пустая, False в противном случае.
    """
    pix = page.get_pixmap(matrix=fitz.Identity, colorspace=fitz.csGRAY)
    pixels = np.frombuffer(pix.samples, dtype=np.uint8)
    total = pixels.size
    blank_count = np.sum(pixels > white_level)
    return blank_count / total > threshold


def is_blank_sync(page, threshold=0.99, white_level=250):
    """
    Проверяет, является ли страница пустой, анализируя содержимое пикселей.

    :param page: Объект страницы PyMuPDF.
    :param threshold: Доля пикселей, которые должны быть "белыми", чтобы страница считалась пустой (по умолчанию: 0.99).
    :param white_level: Значение пикселя, выше которого пиксель считается "белым" (по умолчанию: 250 из 255).
    :return: True, если страница пустая, False в противном случае.
    """
    pix = page.get_pixmap(matrix=fitz.Identity, colorspace=fitz.csGRAY)
    pixels = np.frombuffer(pix.samples, dtype=np.uint8)
    total = pixels.size
    blank_count = np.sum(pixels > white_level)
    return blank_count / total > threshold


async def separate_pages_by_content_async(input_path, threshold=0.99, white_level=250):
    """
    Разделяет страницы PDF на две категории: с данными и пустые, сохраняя их в отдельные файлы.
    """
    if not os.path.exists(input_path):
        raise FileNotFoundError(f"Файл не найден: {input_path}")
    if input_path.lower().endswith('data.pdf'):
        return input_path, None
    if input_path.lower().endswith('empty.pdf'):
        return None, input_path
    
    doc = fitz.open(input_path)
    data_doc = fitz.open()
    empty_doc = fitz.open()

    for pgno in range(len(doc)):
        page = doc.load_page(pgno)
        if await is_blank_async(page, threshold, white_level):
            empty_doc.insert_pdf(doc, from_page=pgno, to_page=pgno)
        else:
            data_doc.insert_pdf(doc, from_page=pgno, to_page=pgno)

    base_name = os.path.splitext(os.path.basename(input_path))[0]
    input_dir = os.path.dirname(input_path)
    if not input_dir:
        input_dir = "."

    data_output_path = os.path.join(input_dir, f"{base_name}-data.pdf")
    empty_output_path = os.path.join(input_dir, f"{base_name}-empty.pdf")

    if len(data_doc) > 0:
        data_doc.save(data_output_path)
    
    # if len(empty_doc) > 0:
    #     empty_doc.save(empty_output_path)

    doc.close()
    data_doc.close()
    empty_doc.close()

    return data_output_path, empty_output_path


def separate_pages_by_content_sync(input_path, threshold=0.99, white_level=250):
    """
    Разделяет страницы PDF на две категории: с данными и пустые, сохраняя их в отдельные файлы.
    """
    if not os.path.exists(input_path):
        raise FileNotFoundError(f"Файл не найден: {input_path}")
    if input_path.lower().endswith('data.pdf'):
        return input_path, None
    if input_path.lower().endswith('empty.pdf'):
        return None, input_path

    doc = fitz.open(input_path)
    data_doc = fitz.open()
    empty_doc = fitz.open()

    for pgno in range(len(doc)):
        page = doc.load_page(pgno)
        if is_blank_sync(page, threshold, white_level):
            empty_doc.insert_pdf(doc, from_page=pgno, to_page=pgno)
        else:
            data_doc.insert_pdf(doc, from_page=pgno, to_page=pgno)

    base_name = os.path.splitext(os.path.basename(input_path))[0]
    input_dir = os.path.dirname(input_path)
    if not input_dir:
        input_dir = "."

    data_output_path = os.path.join(input_dir, f"{base_name}-data.pdf")
    empty_output_path = os.path.join(input_dir, f"{base_name}-empty.pdf")

    if len(data_doc) > 0:
        data_doc.save(data_output_path)

    # if len(empty_doc) > 0:
    #     empty_doc.save(empty_output_path)

    doc.close()
    data_doc.close()
    empty_doc.close()

    return data_output_path, empty_output_path


async def divide_documents(input_path):
    """
    Извлекает каждый документ из PDF в отдельный файл.
    """
    data_path, empty_path = await separate_pages_by_content_async(input_path)
    print(f"Данные: {data_path}, Пустые: {empty_path}")
    return data_path, empty_path


if __name__ == "__main__":
    # Пример использования
    file_path = r"c:\Scan\All\AlreadyAddToDb\2025-08-22_092958.pdf "
    asyncio.run(divide_documents(file_path))
