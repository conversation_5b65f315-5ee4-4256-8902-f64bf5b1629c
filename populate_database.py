# === Импорт необходимых библиотек ===
import os
import shutil
from pypdf import PdfReader, PdfWriter
# ИСПРАВЛЕНИЕ: Единственно верный способ импортировать основной класс из Docling
from docling.pipeline.docling_pipeline import DoclingPipeline
from langchain.schema import Document
from langchain.text_splitter import MarkdownHeaderTextSplitter
from langchain_community.vectorstores import Chroma
from langchain_community.embeddings import OllamaEmbeddings
from tqdm import tqdm

# === Основные настройки (Константы) ===

DATA_PATH = "data"
CHROMA_PATH = "chroma"
TEMP_PATH = "temp_docs"
DOCUMENT_KEYWORDS = {
    "договір": "Договір",
    "акт": "Акт",
    "рахунок": "Рахунок-фактура",
    "додаток": "Додаток",
    "специфікація": "Специфікація",
}

def split_pdf_into_logical_documents(filepath: str) -> list[dict]:
    """
    Разделяет один большой PDF-файл на несколько логических документов.
    """
    reader = PdfReader(filepath)
    logical_docs = []
    current_pages = []
    doc_type = "Невідомий"
    start_page = 0

    for i, page in enumerate(reader.pages):
        text = page.extract_text().lower()
        
        found_new_doc = False
        for keyword, type_name in DOCUMENT_KEYWORDS.items():
            if keyword in text[:150]:
                if current_pages:
                    logical_docs.append({
                        "pages": current_pages,
                        "type": doc_type,
                        "start_page": start_page
                    })
                
                current_pages = [page]
                doc_type = type_name
                start_page = i
                found_new_doc = True
                break
        
        if not found_new_doc:
            current_pages.append(page)

    if current_pages:
        logical_docs.append({
            "pages": current_pages,
            "type": doc_type,
            "start_page": start_page
        })
        
    output_files = []
    base_filename = os.path.basename(filepath)
    if not os.path.exists(TEMP_PATH):
        os.makedirs(TEMP_PATH)

    for i, doc_info in enumerate(logical_docs):
        writer = PdfWriter()
        for page in doc_info["pages"]:
            writer.add_page(page)
        
        temp_filename = f"{os.path.splitext(base_filename)[0]}_part_{i}.pdf"
        temp_filepath = os.path.join(TEMP_PATH, temp_filename)
        with open(temp_filepath, "wb") as f:
            writer.write(f)
        
        output_files.append({
            "path": temp_filepath,
            "type": doc_info["type"],
            "original_source": base_filename,
            "original_pages": list(range(doc_info["start_page"] + 1, doc_info["start_page"] + len(doc_info["pages"]) + 1))
        })
        
    return output_files


def process_document_with_docling(filepath: str, docling_pipe) -> str:
    """
    Обрабатывает один PDF-файл с помощью Docling, извлекая текст и структуру.
    """
    try:
        doc = docling_pipe(filepath)
        markdown_output = doc.to_markdown()
        return markdown_output
    except Exception as e:
        print(f"Ошибка при обработке файла '{os.path.basename(filepath)}' с помощью Docling: {e}")
        return ""

def get_chunks_from_markdown(markdown_text: str, metadata: dict) -> list[Document]:
    """
    Разделяет текст в формате Markdown на чанки по заголовкам.
    """
    headers_to_split_on = [("#", "Header 1"), ("##", "Header 2")]
    markdown_splitter = MarkdownHeaderTextSplitter(headers_to_split_on=headers_to_split_on)
    fragments = markdown_splitter.split_text(markdown_text)
    
    all_docs = []
    for fragment in fragments:
        new_metadata = metadata.copy()
        new_metadata.update(fragment.metadata)
        doc = Document(page_content=fragment.page_content, metadata=new_metadata)
        all_docs.append(doc)
    return all_docs

def get_embedding_function():
    """Возвращает функцию для создания эмбеддингов."""
    return OllamaEmbeddings(model="nomic-embed-text")

def add_to_chroma(chunks: list[Document]):
    """Добавляет чанки в базу данных ChromaDB, избегая дубликатов."""
    db = Chroma(
        persist_directory=CHROMA_PATH, embedding_function=get_embedding_function()
    )

    for i, chunk in enumerate(chunks):
        chunk.metadata["id"] = f"{chunk.metadata['source']}-{chunk.metadata.get('pages', '')}-{i}"

    existing_ids = set(db.get(include=[])["ids"])
    new_chunks = [chunk for chunk in chunks if chunk.metadata["id"] not in existing_ids]

    if new_chunks:
        print(f"Добавляем {len(new_chunks)} новых чанков в базу.")
        new_chunk_ids = [chunk.metadata["id"] for chunk in new_chunks]
        db.add_documents(new_chunks, ids=new_chunk_ids)
        db.persist()
    else:
        print("Новых документов для добавления нет.")

# === Основной блок выполнения скрипта ===
if __name__ == "__main__":
    
    # ИСПРАВЛЕНИЕ: Инициализируем модель Docling правильным способом.
    print("Загрузка AI-модели Docling в память... (это может занять несколько минут при первом запуске)")
    docling_processor = DoclingPipeline()
    print("Модель успешно загружена.")

    all_chunks = []
    
    print("\nЭтап 1: Разделение PDF на логические документы...")
    for filename in tqdm(os.listdir(DATA_PATH), desc="Обработка файлов"):
        if filename.lower().endswith(".pdf"):
            filepath = os.path.join(DATA_PATH, filename)
            
            logical_doc_files = split_pdf_into_logical_documents(filepath)
            
            # Используем вложенный tqdm для отображения прогресса по частям одного файла
            for doc_file_info in tqdm(logical_doc_files, desc=f"Части файла {filename}", leave=False):
                markdown_content = process_document_with_docling(doc_file_info["path"], docling_processor)
                
                if markdown_content:
                    metadata = {
                        "source": doc_file_info["original_source"],
                        "doc_type": doc_file_info["type"],
                        "pages": ", ".join(map(str, doc_file_info["original_pages"]))
                    }
                    chunks = get_chunks_from_markdown(markdown_content, metadata)
                    all_chunks.extend(chunks)

    if all_chunks:
        print("\nЭтап 3: Добавление новых чанков в векторную базу данных...")
        add_to_chroma(all_chunks)
    else:
        print("Не найдено документов для обработки.")

    if os.path.exists(TEMP_PATH):
        shutil.rmtree(TEMP_PATH)
    
    print("\nОбработка завершена.")