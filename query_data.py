# -*- coding: utf-8 -*-
"""
query_data.py


https://aistudio.google.com/prompts/15RQZ2ShFI8lkvFiqZ5IsrGkHZfbqq7bf

Скрипт для поиска информации о клиенте в базе данных ChromaDB.
"""


# query_data.py

import argparse
from langchain_community.vectorstores import Chroma
from langchain_community.chat_models import ChatOllama
from langchain.prompts import ChatPromptTemplate
from populate_database import get_embedding_function

# Путь к нашей векторной базе
CHROMA_PATH = "chroma"

# Это "сердце" нашей системы. Промпт - это инструкция для языковой модели.
# Мы четко говорим ей, что делать, на основе каких данных, и в каком формате дать ответ.
# {context} - сюда будут подставлены найденные в базе чанки.
# {question} - сюда будет подставлен ваш запрос (имя клиента).
PROMPT_TEMPLATE = """
Ти — корисний помічник, який відповідає на запитання, грунтуючись виключно на наданому контексті.

Контекст:
{context}

---

Завдання:
Проаналізуй наведений вище контекст і знайди всю інформацію, що стосується клієнта "{question}".
Для кожного знайденого документа надай чітку та структуровану відповідь у наступному форматі:

**Документ:** [назва файлу з метаданих 'source']
- **Тип:** [тип документа з метаданих 'doc_type']
- **Сторінки в оригінальному файлі:** [номери сторінок з метаданих 'pages']
- **Зміст:** [короткий виклад знайденої інформації про клієнта в цьому фрагменті]

Якщо в одному файлі є кілька згадок, перелічи їх усі під заголовком цього файлу.
Якщо інформація про клієнта в наданому контексті відсутня, дай відповідь: "Інформація по клієнту '{question}' в наданих документах не знайдена."
"""

def main():
    # Настраиваем возможность передавать имя клиента как аргумент командной строки
    # Пример: python query_data.py "ТОВ Ромашка"
    parser = argparse.ArgumentParser(description="Пошук інформації про клієнта в документах.")
    parser.add_argument("query_text", type=str, help="Ім'я клієнта для пошуку.")
    args = parser.parse_args()
    query_text = args.query_text

    # Подключаемся к нашей векторной базе данных
    embedding_function = get_embedding_function()
    db = Chroma(persist_directory=CHROMA_PATH, embedding_function=embedding_function)

    # Выполняем семантический поиск.
    # Ищем 10 наиболее релевантных чанков, чтобы дать модели больше контекста.
    results = db.similarity_search(query_text, k=10)

    if not results:
        print(f"Інформація по клієнту '{query_text}' в базі даних не знайдена.")
        return

    # Формируем контекст из найденных чанков.
    # Добавляем метаданные к каждому чанку, чтобы модель знала источник.
    context_text = ""
    for doc in results:
        context_text += f"Джерело: {doc.metadata['source']}, Тип: {doc.metadata.get('doc_type', 'N/A')}, Сторінки: {doc.metadata.get('pages', 'N/A')}\n"
        context_text += doc.page_content + "\n\n---\n\n"

    # Создаем промпт на основе нашего шаблона
    prompt_template = ChatPromptTemplate.from_template(PROMPT_TEMPLATE)
    prompt = prompt_template.format(context=context_text, question=query_text)
    
    # Инициализируем языковую модель (например, Llama 3 через Ollama)
    # Убедитесь, что модель скачана: `ollama pull llama3`
    model = ChatOllama(model="llama3")

    # Отправляем промпт в модель и получаем ответ
    print(f"Запит до моделі по клієнту: '{query_text}'...")
    response = model.invoke(prompt)

    # Выводим результат
    print("\n--- Результат пошуку ---\n")
    print(response.content)
    print("\n--- Кінець результату ---\n")

if __name__ == "__main__":
    main()